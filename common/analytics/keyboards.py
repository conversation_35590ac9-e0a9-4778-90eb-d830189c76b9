from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button, get_universal_back_button
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта для импорта database
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database import StudentRepository, GroupRepository, CuratorRepository

def get_analytics_menu_kb(role: str) -> InlineKeyboardMarkup:
    """Клавиатура меню аналитики"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📊 Статистика по ученику", callback_data="student_analytics")],
        [InlineKeyboardButton(text="📈 Статистика по группе", callback_data="group_analytics")],
        *get_main_menu_back_button()
    ])

async def get_groups_for_analytics_kb(role: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора группы для аналитики"""
    # Получаем реальные группы из базы данных
    try:
        groups = await GroupRepository.get_all()
    except Exception as e:
        print(f"Ошибка при получении групп: {e}")
        groups = []

    buttons = []
    for group in groups:
        # Показываем название группы с предметом
        group_name = f"{group.name} ({group.subject.name})" if group.subject else group.name
        buttons.append([
            InlineKeyboardButton(
                text=group_name,
                callback_data=f"analytics_group_{group.id}"
            )
        ])

    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text="❌ Группы не найдены",
                callback_data="no_groups"
            )
        ])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_students_for_analytics_kb(group_id: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора ученика для аналитики"""
    # Получаем реальных студентов из базы данных
    try:
        students = await StudentRepository.get_by_group(int(group_id))
    except Exception as e:
        print(f"Ошибка при получении студентов: {e}")
        students = []

    buttons = []
    for student in students:
        buttons.append([
            InlineKeyboardButton(
                text=student.user.name,
                callback_data=f"analytics_student_{student.id}"
            )
        ])

    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text="❌ Студенты не найдены",
                callback_data="no_students"
            )
        ])

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_groups_by_curator_kb(curator_id: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора группы конкретного куратора для аналитики"""
    try:
        # Получаем все группы куратора
        curator_groups = await CuratorRepository.get_curator_groups(int(curator_id))

        if not curator_groups:
            # Если у куратора нет групп, показываем все группы
            return await get_groups_for_analytics_kb("manager")

        # Показываем все группы этого куратора
        buttons = []
        for group in curator_groups:
            group_name = f"{group.name} ({group.subject.name})" if group.subject else group.name
            buttons.append([
                InlineKeyboardButton(
                    text=group_name,
                    callback_data=f"analytics_group_{group.id}"
                )
            ])

    except Exception as e:
        print(f"Ошибка при получении групп куратора: {e}")
        # В случае ошибки показываем все группы
        return await get_groups_for_analytics_kb("manager")

    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)


async def get_curators_with_groups_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора куратора с группировкой по кураторам для статистики по группам"""
    try:
        # Получаем всех кураторов
        curators = await CuratorRepository.get_all()

        # Группируем кураторов по пользователям
        curator_users = {}
        for curator in curators:
            user_id = curator.user_id
            if user_id not in curator_users:
                curator_users[user_id] = {
                    'user_name': curator.user.name,
                    'curator_id': curator.id,  # Берем первый ID для callback
                    'groups': []
                }
            if curator.group:
                curator_users[user_id]['groups'].append(curator.group)

        buttons = []
        for user_data in curator_users.values():
            if user_data['groups']:
                # Показываем куратора с количеством групп
                group_count = len(user_data['groups'])
                # Получаем уникальные предметы
                subjects = list(set(group.subject.name for group in user_data['groups'] if group.subject))
                subjects_text = ", ".join(subjects) if subjects else "Без предмета"

                curator_text = f"{user_data['user_name']} - {subjects_text} ({group_count} групп)"
                buttons.append([
                    InlineKeyboardButton(
                        text=curator_text,
                        callback_data=f"manager_curator_{user_data['curator_id']}"
                    )
                ])

        if not buttons:
            buttons.append([
                InlineKeyboardButton(
                    text="❌ Кураторы с группами не найдены",
                    callback_data="no_curators"
                )
            ])

    except Exception as e:
        print(f"Ошибка при получении кураторов: {e}")
        buttons = [[
            InlineKeyboardButton(
                text="❌ Ошибка загрузки кураторов",
                callback_data="error_curators"
            )
        ]]

    buttons.extend(get_main_menu_back_button())
    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_back_to_analytics_kb() -> InlineKeyboardMarkup:
    """Клавиатура возврата в меню аналитики"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button()
    ])