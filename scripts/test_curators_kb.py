"""
Тестовый скрипт для проверки клавиатуры кураторов
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database, CuratorRepository
from manager.keyboards.analytics import get_curators_kb


async def test_curators_keyboard():
    """Тестируем клавиатуру кураторов"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("👥 Получение кураторов из базы данных...")
    
    # Получаем всех кураторов
    curators = await CuratorRepository.get_all()
    print(f"✅ Найдено кураторов в базе: {len(curators)}")
    
    for curator in curators:
        print(f"   • {curator.user.name} (ID: {curator.id})")
        if curator.subject:
            print(f"     Предмет: {curator.subject.name}")
        if curator.group:
            print(f"     Группа: {curator.group.name}")
        print()
    
    print("🔧 Тестирование клавиатуры кураторов...")
    
    # Тестируем клавиатуру
    try:
        keyboard = get_curators_kb()
        print("✅ Клавиатура кураторов создана успешно")
        
        # Показываем кнопки
        for row in keyboard.inline_keyboard:
            for button in row:
                print(f"   Кнопка: {button.text} -> {button.callback_data}")
    
    except Exception as e:
        print(f"❌ Ошибка при создании клавиатуры: {e}")
    
    print(f"\n🎉 Тестирование завершено!")


if __name__ == "__main__":
    asyncio.run(test_curators_keyboard())
