"""
Тестовый скрипт для проверки интеграции аналитики
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database, StudentRepository
from common.statistics import get_real_student_analytics


async def test_analytics_integration():
    """Тестируем интеграцию аналитики"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📊 Тестирование аналитики студентов...")
    
    # Получаем всех студентов
    students = await StudentRepository.get_all()
    if not students:
        print("❌ Студенты не найдены")
        return
    
    print(f"✅ Найдено студентов: {len(students)}")
    
    # Найдем студентов с результатами ДЗ
    students_with_results = []
    for student in students:
        general_stats = await StudentRepository.get_general_stats(student.id)
        if general_stats.get('total_points', 0) > 0:
            students_with_results.append(student)

    print(f"✅ Студентов с результатами ДЗ: {len(students_with_results)}")

    # Тестируем аналитику для студентов с результатами
    test_students = students_with_results[:3] if students_with_results else students[:3]

    for i, student in enumerate(test_students):
        print(f"\n{'='*50}")
        print(f"Тестируем студента {i+1}: {student.user.name}")
        print(f"{'='*50}")

        # Получаем аналитику
        analytics_text = await get_real_student_analytics(student.id)
        print(analytics_text)
    
    print(f"\n🎉 Тестирование завершено!")


if __name__ == "__main__":
    asyncio.run(test_analytics_integration())
