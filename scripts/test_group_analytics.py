"""
Тестовый скрипт для проверки статистики по группам
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database, GroupRepository, CuratorRepository
from common.statistics import get_real_group_analytics


async def test_group_analytics():
    """Тестируем статистику по группам"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📊 Тестирование статистики групп...")
    
    # Получаем все группы
    groups = await GroupRepository.get_all()
    if not groups:
        print("❌ Группы не найдены")
        return
    
    print(f"✅ Найдено групп: {len(groups)}")
    
    # Тестируем статистику для первых 3 групп
    test_groups = groups[:3]

    for i, group in enumerate(test_groups):
        print(f"\n{'='*60}")
        print(f"Тестируем группу {i+1}: {group.name}")
        if group.subject:
            print(f"Предмет: {group.subject.name}")
        print(f"{'='*60}")

        # Получаем аналитику
        analytics_text = await get_real_group_analytics(group.id)
        print(analytics_text)
    
    print(f"\n🎉 Тестирование групп завершено!")
    
    # Тестируем клавиатуру кураторов
    print(f"\n{'='*60}")
    print("Тестируем клавиатуру кураторов с группами...")
    print(f"{'='*60}")
    
    curators = await CuratorRepository.get_all()
    print(f"✅ Найдено кураторов: {len(curators)}")
    
    for curator in curators:
        if curator.group and curator.group.subject:
            curator_text = f"{curator.user.name} - {curator.group.name} ({curator.group.subject.name})"
            print(f"• {curator_text}")


if __name__ == "__main__":
    asyncio.run(test_group_analytics())
