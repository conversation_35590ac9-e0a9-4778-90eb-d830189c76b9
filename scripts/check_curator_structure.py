"""
Проверка структуры кураторов и групп
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database
from sqlalchemy import select, text
from database.database import get_db_session


async def check_database_structure():
    await init_database()
    
    async with get_db_session() as session:
        # Проверяем, есть ли таблица связи кураторов и групп
        query = text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND (table_name LIKE '%curator%' OR table_name LIKE '%group%')")
        result = await session.execute(query)
        
        print('Таблицы связанные с кураторами и группами:')
        for row in result:
            print(f'  - {row[0]}')
        
        # Проверяем структуру таблицы кураторов
        query2 = text("SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_name = 'curators' ORDER BY ordinal_position")
        result2 = await session.execute(query2)
        
        print('\nСтруктура таблицы curators:')
        for row in result2:
            print(f'  {row[0]:15} | {row[1]:15} | nullable: {row[2]:3} | default: {row[3]}')


if __name__ == "__main__":
    asyncio.run(check_database_structure())
