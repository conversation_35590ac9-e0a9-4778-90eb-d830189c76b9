"""
Тестовый скрипт для демонстрации статистики студентов
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database,
    StudentRepository,
    SubjectRepository,
    MicrotopicRepository,
    HomeworkResultRepository,
    get_db_session
)


async def show_student_stats():
    """Показать статистику студентов"""
    print("📊 Демонстрация статистики студентов")
    print("=" * 50)
    
    # Получаем всех студентов
    students = await StudentRepository.get_all()
    if not students:
        print("❌ Студенты не найдены")
        return
    
    # Показываем статистику для первых 5 студентов
    for i, student in enumerate(students[:5]):
        print(f"\n👤 Студент: {student.user.name}")
        print(f"📚 Группа: {student.group.name if student.group else 'Не назначена'}")
        print(f"💎 Тариф: {student.tariff or 'Не указан'}")
        
        # Общая статистика
        general_stats = await StudentRepository.get_general_stats(student.id)
        print(f"📊 Общая статистика:")
        print(f"   • Баллы: {general_stats.get('total_points', 0)}")
        print(f"   • Уровень: {student.level}")
        print(f"   • Выполнено ДЗ: {general_stats.get('total_completed', 0)}")
        
        # Статистика по предметам (если студент в группе)
        if student.group and student.group.subject:
            subject = student.group.subject
            print(f"\n📗 Прогресс по предмету '{subject.name}':")
            
            # Получаем понимание по микротемам
            microtopic_stats = await StudentRepository.get_microtopic_understanding(
                student.id, subject.id
            )
            
            if microtopic_stats:
                # Получаем названия микротем
                microtopics = await MicrotopicRepository.get_by_subject(subject.id)
                microtopic_names = {mt.number: mt.name for mt in microtopics}
                
                strong_topics = []  # ≥80%
                weak_topics = []    # ≤40%
                
                for number, stats in microtopic_stats.items():
                    name = microtopic_names.get(number, f"Микротема {number}")
                    percentage = stats['percentage']
                    total = stats['total_answered']
                    correct = stats['correct_answered']
                    
                    print(f"   • {name} — {percentage:.0f}% ({correct}/{total})")
                    
                    if percentage >= 80:
                        strong_topics.append(name)
                    elif percentage <= 40:
                        weak_topics.append(name)
                
                # Показываем сильные и слабые темы
                if strong_topics:
                    print(f"\n🟢 Сильные темы (≥80%):")
                    for topic in strong_topics:
                        print(f"   • {topic}")
                
                if weak_topics:
                    print(f"\n🔴 Слабые темы (≤40%):")
                    for topic in weak_topics:
                        print(f"   • {topic}")
                
                # Показываем непроверенные микротемы
                all_microtopic_numbers = set(microtopic_names.keys())
                tested_numbers = set(microtopic_stats.keys())
                untested_numbers = all_microtopic_numbers - tested_numbers
                
                if untested_numbers:
                    print(f"\n❌ Не проверено:")
                    for number in sorted(untested_numbers):
                        name = microtopic_names.get(number, f"Микротема {number}")
                        print(f"   • {name}")
            else:
                print("   ❌ Нет данных о прохождении ДЗ по этому предмету")
        
        print("-" * 40)


async def show_detailed_homework_results():
    """Показать детальные результаты ДЗ"""
    print("\n📋 Детальные результаты домашних заданий")
    print("=" * 50)
    
    # Получаем первого студента для примера
    students = await StudentRepository.get_all()
    if not students:
        print("❌ Студенты не найдены")
        return
    
    student = students[0]
    print(f"👤 Студент: {student.user.name}")
    
    # Получаем все результаты студента
    homework_results = await HomeworkResultRepository.get_by_student(student.id)
    
    if not homework_results:
        print("❌ Результаты ДЗ не найдены")
        return
    
    print(f"📊 Найдено результатов: {len(homework_results)}")
    
    for result in homework_results:
        percentage = (result.correct_answers / result.total_questions * 100) if result.total_questions > 0 else 0
        attempt_type = "Первая попытка" if result.is_first_attempt else "Повторная попытка"
        
        print(f"\n📝 ДЗ: {result.homework.name}")
        print(f"   📚 Предмет: {result.homework.subject.name}")
        print(f"   📊 Результат: {result.correct_answers}/{result.total_questions} ({percentage:.0f}%)")
        print(f"   🎯 Баллы: {result.points_earned}")
        print(f"   🔄 Тип: {attempt_type}")
        print(f"   📅 Дата: {result.completed_at.strftime('%d.%m.%Y %H:%M')}")


async def main():
    """Главная функция"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    await show_student_stats()
    await show_detailed_homework_results()
    
    print("\n🎉 Демонстрация завершена!")


if __name__ == "__main__":
    asyncio.run(main())
