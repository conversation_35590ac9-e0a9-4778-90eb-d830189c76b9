"""
Тестовый скрипт для проверки потока аналитики менеджера
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database
from common.analytics.keyboards import get_curators_with_groups_kb, get_groups_by_curator_kb
from common.statistics import get_real_group_analytics


async def test_manager_analytics_flow():
    """Тестируем поток аналитики менеджера"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("\n📊 Тестирование потока аналитики менеджера...")
    
    # Шаг 1: Тестируем клавиатуру кураторов с группами
    print("\n" + "="*60)
    print("Шаг 1: Клавиатура выбора куратора с группами")
    print("="*60)
    
    curators_kb = await get_curators_with_groups_kb()
    print(f"✅ Клавиатура создана с {len(curators_kb.inline_keyboard)} кнопками")
    
    for i, row in enumerate(curators_kb.inline_keyboard):
        if row[0].callback_data.startswith("manager_curator_"):
            curator_id = row[0].callback_data.replace("manager_curator_", "")
            print(f"  {i+1}. {row[0].text} (ID: {curator_id})")
    
    # Шаг 2: Тестируем выбор группы куратора
    print("\n" + "="*60)
    print("Шаг 2: Выбор группы куратора")
    print("="*60)
    
    # Берем первого куратора
    first_curator_button = None
    for row in curators_kb.inline_keyboard:
        if row[0].callback_data.startswith("manager_curator_"):
            first_curator_button = row[0]
            break
    
    if first_curator_button:
        curator_id = first_curator_button.callback_data.replace("manager_curator_", "")
        print(f"Выбран куратор: {first_curator_button.text}")
        
        # Получаем группы куратора
        groups_kb = await get_groups_by_curator_kb(curator_id)
        print(f"✅ Клавиатура групп создана с {len(groups_kb.inline_keyboard)} кнопками")
        
        for i, row in enumerate(groups_kb.inline_keyboard):
            if row[0].callback_data.startswith("analytics_group_"):
                group_id = row[0].callback_data.replace("analytics_group_", "")
                print(f"  {i+1}. {row[0].text} (ID: {group_id})")
        
        # Шаг 3: Тестируем статистику группы
        print("\n" + "="*60)
        print("Шаг 3: Статистика группы")
        print("="*60)
        
        # Берем первую группу
        first_group_button = None
        for row in groups_kb.inline_keyboard:
            if row[0].callback_data.startswith("analytics_group_"):
                first_group_button = row[0]
                break
        
        if first_group_button:
            group_id = first_group_button.callback_data.replace("analytics_group_", "")
            print(f"Выбрана группа: {first_group_button.text}")
            
            # Получаем статистику
            analytics_text = await get_real_group_analytics(int(group_id))
            print("\n📊 Статистика группы:")
            print("-" * 40)
            print(analytics_text)
            print("-" * 40)
    
    print(f"\n🎉 Тестирование потока завершено!")


if __name__ == "__main__":
    asyncio.run(test_manager_analytics_flow())
