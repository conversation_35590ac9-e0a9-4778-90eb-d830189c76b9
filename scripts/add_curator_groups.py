"""
Скрипт для добавления дополнительных групп кураторам
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database,
    CuratorRepository,
    GroupRepository,
    SubjectRepository,
    UserRepository
)


async def add_curator_groups():
    """Добавляем дополнительные группы кураторам"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📊 Добавление дополнительных групп кураторам...")
    
    # Получаем всех кураторов
    curators = await CuratorRepository.get_all()
    print(f"✅ Найдено кураторов: {len(curators)}")
    
    # Получаем все группы
    groups = await GroupRepository.get_all()
    print(f"✅ Найдено групп: {len(groups)}")
    
    # Добавляем дополнительные группы кураторам
    for curator in curators:
        print(f"\n👤 Куратор: {curator.user.name}")
        
        # Найдем группы того же предмета, что и у куратора
        if curator.subject:
            subject_groups = [g for g in groups if g.subject_id == curator.subject_id and g.id != curator.group_id]
            
            print(f"📚 Предмет: {curator.subject.name}")
            print(f"🎯 Текущая группа: {curator.group.name if curator.group else 'Нет'}")
            print(f"📋 Доступные группы того же предмета: {len(subject_groups)}")
            
            # Добавляем первую доступную группу как дополнительную
            if subject_groups:
                additional_group = subject_groups[0]
                try:
                    # Создаем дополнительную запись куратора для новой группы
                    await CuratorRepository.create(
                        user_id=curator.user_id,
                        course_id=curator.course_id,
                        subject_id=curator.subject_id,
                        group_id=additional_group.id
                    )
                    print(f"   ✅ Добавлена группа: {additional_group.name}")
                except Exception as e:
                    print(f"   ❌ Ошибка при добавлении группы: {e}")
            else:
                print("   ⚠️ Нет дополнительных групп для добавления")
        else:
            print("   ⚠️ У куратора нет предмета")
    
    print(f"\n🎉 Добавление групп завершено!")
    
    # Проверяем результат
    print(f"\n{'='*60}")
    print("📊 РЕЗУЛЬТАТ:")
    print(f"{'='*60}")
    
    curators_after = await CuratorRepository.get_all()
    
    # Группируем по пользователям
    curator_users = {}
    for curator in curators_after:
        user_id = curator.user_id
        if user_id not in curator_users:
            curator_users[user_id] = {
                'user_name': curator.user.name,
                'groups': []
            }
        if curator.group:
            curator_users[user_id]['groups'].append(curator.group)
    
    for user_data in curator_users.values():
        print(f"\n👤 {user_data['user_name']}:")
        if user_data['groups']:
            for group in user_data['groups']:
                subject_name = group.subject.name if group.subject else "Без предмета"
                print(f"   📚 {group.name} ({subject_name})")
        else:
            print("   ❌ Нет групп")


if __name__ == "__main__":
    asyncio.run(add_curator_groups())
