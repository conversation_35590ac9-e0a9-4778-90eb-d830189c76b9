"""
Тест для проверки валидации микротем в тестах месяца
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    init_database,
    CourseRepository,
    SubjectRepository,
    MicrotopicRepository,
    MonthTestRepository,
    MonthTestMicrotopicRepository
)


async def test_microtopic_validation():
    """Тестируем валидацию микротем"""
    print("🧪 Тестирование валидации микротем для тестов месяца...")
    
    # Инициализируем базу данных
    await init_database()
    
    # Получаем существующие курсы и предметы
    courses = await CourseRepository.get_all()
    subjects = await SubjectRepository.get_all()
    
    if not courses or not subjects:
        print("❌ Нет курсов или предметов в базе данных")
        return
    
    # Берем первый курс и предмет
    course = courses[0]
    subject = subjects[0]
    
    print(f"📚 Используем курс: {course.name}")
    print(f"📖 Используем предмет: {subject.name}")
    
    # Получаем микротемы для этого предмета
    microtopics = await MicrotopicRepository.get_by_subject(subject.id)
    
    if not microtopics:
        print("❌ Нет микротем для данного предмета")
        print("📝 Создаем тестовые микротемы...")
        
        # Создаем несколько тестовых микротем
        test_microtopics = ["Тестовая микротема 1", "Тестовая микротема 2", "Тестовая микротема 3"]
        for name in test_microtopics:
            try:
                microtopic = await MicrotopicRepository.create(name, subject.id)
                print(f"   ✅ Создана микротема: {microtopic.number}. {microtopic.name}")
            except Exception as e:
                print(f"   ❌ Ошибка при создании микротемы '{name}': {e}")
        
        # Обновляем список микротем
        microtopics = await MicrotopicRepository.get_by_subject(subject.id)
    
    if not microtopics:
        print("❌ Не удалось создать микротемы")
        return
    
    print(f"📋 Доступные микротемы для предмета '{subject.name}':")
    for mt in microtopics:
        print(f"   {mt.number}. {mt.name}")
    
    # Тестируем валидацию
    print("\n🔍 Тестируем валидацию номеров микротем:")
    
    # Тест 1: Существующие номера
    existing_numbers = [mt.number for mt in microtopics[:2]]  # Берем первые 2
    print(f"\n1️⃣ Тест с существующими номерами: {existing_numbers}")
    
    for number in existing_numbers:
        exists = await MicrotopicRepository.exists_by_number(subject.id, number)
        print(f"   Номер {number}: {'✅ существует' if exists else '❌ не существует'}")
    
    # Тест 2: Несуществующие номера
    non_existing_numbers = [999, 1000]
    print(f"\n2️⃣ Тест с несуществующими номерами: {non_existing_numbers}")
    
    for number in non_existing_numbers:
        exists = await MicrotopicRepository.exists_by_number(subject.id, number)
        print(f"   Номер {number}: {'✅ существует' if exists else '❌ не существует'}")
    
    # Тест 3: Смешанные номера
    mixed_numbers = existing_numbers + [999]
    print(f"\n3️⃣ Тест со смешанными номерами: {mixed_numbers}")
    
    valid_numbers = []
    invalid_numbers = []
    
    for number in mixed_numbers:
        exists = await MicrotopicRepository.exists_by_number(subject.id, number)
        if exists:
            valid_numbers.append(number)
        else:
            invalid_numbers.append(number)
    
    print(f"   Валидные номера: {valid_numbers}")
    print(f"   Невалидные номера: {invalid_numbers}")
    
    # Тест 4: Создание теста месяца с валидными номерами
    if valid_numbers:
        print(f"\n4️⃣ Создаем тест месяца с валидными номерами: {valid_numbers}")
        
        try:
            # Создаем тест месяца
            month_test = await MonthTestRepository.create(
                name="Тестовый месяц",
                course_id=course.id,
                subject_id=subject.id
            )
            print(f"   ✅ Создан тест месяца: {month_test.name} (ID: {month_test.id})")
            
            # Привязываем микротемы
            for number in valid_numbers:
                try:
                    relation = await MonthTestMicrotopicRepository.create(
                        month_test_id=month_test.id,
                        microtopic_number=number
                    )
                    print(f"   ✅ Привязана микротема {number}")
                except Exception as e:
                    print(f"   ❌ Ошибка при привязке микротемы {number}: {e}")
            
            # Проверяем созданный тест
            created_test = await MonthTestRepository.get_by_id(month_test.id)
            if created_test:
                microtopic_numbers = [mt.microtopic_number for mt in created_test.microtopics]
                print(f"   📋 Привязанные микротемы: {sorted(microtopic_numbers)}")
            
        except Exception as e:
            print(f"   ❌ Ошибка при создании теста месяца: {e}")
    
    print("\n🎉 Тестирование завершено!")


if __name__ == "__main__":
    asyncio.run(test_microtopic_validation())
